<div class="row mr-0 ml-0 clo-table-content">
    <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pl-0 pr-3 pt-3 pb-3 table-border">
        <div class="float-left pl-3 Heading2-M pt-2 d-flex align-items-center">
            {{ tableTitle }}
        </div>
        <div class="float-right">
            <input #fileInput type="file" style="display: none" accept=".xlsx,.xls" (change)="onFileSelected($event)" />
            <button *ngIf="permissions?.[tableName]?.canExport" id="btn-export-excel" (click)="exportToExcel()" 
                class="btn TextTruncate btn-warning-export mr-2 export-btn">
                <img src="assets/dist/images/Vector.svg" class="align-middle me-2" alt="Export Icon" />
                Export
            </button>

            <button *ngIf="permissions?.[tableName]?.canImport" id="btn-import" class="btn btn-primary btn-import-template" (click)="openUpload()">
                Import 
            </button>

            <button kendoButton id="btn-download-template" (click)="downloadTemplate()" (keypress)="downloadTemplate()"
                class="btn btn-link pr-2 pl-2 btn-primary mr-2 btn-download-template"
                title="Click here to Download Template">
                <img src="assets/dist/images/FiDownload.svg" alt="Icon" class="align-middle" />
            </button>
          
            <button  class="btn TextTruncate threeDots-btn kebab-button p-0" #anchor
            (click)="openMenu($event)">&#x22EE;
            <kendo-popup [anchor]="anchor" *ngIf="isMenuOpen" [anchorAlign]="{ vertical: 'bottom', horizontal: 'right' }"
              [popupAlign]="{ vertical: 'top', horizontal: 'right' }">
              <kendo-menu [items]="menuItems" (select)="onSelect($event)">
                <ng-template kendoMenuItemTemplate let-item>
                  <span>
                    <img src="assets/dist/images/delete-Table.svg" alt="" width="16" height="16" style="margin-right: 8px;">
                    {{ menuItems[0].text }}
                  </span>
                </ng-template>
              </kendo-menu>
            </kendo-popup>
          </button>
        </div>
    </div>
<ng-container *ngIf="isStaticTable">
    <app-static-data-table ></app-static-data-table>
    <div class="row mr-0 ml-0 w-100">
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pl-0 pr-0">
            <div>
                <div class="nodata-container">
                    <img src="assets/dist/images/clonodata.svg" alt="No Data" />
                </div>
            </div>
        </div>
    </div>
</ng-container>
 <ng-container *ngIf="!isStaticTable">
    <app-flat-data-table [periodId]="periodId" [managedAccountId]="managedAccountId" [moduleName]="moduleName"></app-flat-data-table>
    <div class="row mr-0 ml-0 w-100">
        <div class="col-12 col-sm-12 col-md-12 col-lg-12 col-xl-12 pl-0 pr-0">
            <div>
                <div class="nodata-container">
                    <img src="assets/dist/images/clonodata.svg" alt="No Data" />
                </div>
            </div>
        </div>
    </div>
</ng-container> 
    
    <div class="clo-table-body col-12 col-lg-12 col-xl-12 col-sm-12 col-lg-12 col-md-12 pr-0 pl-0 Heading2-M">
        <app-data-table-footnote [footnotes]="footnotes" (save)="handleSave($event)" (cancel)="handleCancel($event)" (reset)="handleReset($event)">
        </app-data-table-footnote>
    </div>
    <div *ngIf="showPopup">
        <app-delete-confirmation-modal [confirmBtn]="'Yes, Delete'" [cancelBtn]="'No, keep it'" 
          [modalTitle]="'Delete Confirmation'" [deletedCloName]="tableName" [isTableLevel]="true" 
          [deleteNoteType]="'Company'" (PrimaryButtonEventHandler)="deleteItem()"
         (SecondaryButtonEventHandler)="hideDeletePopup()"></app-delete-confirmation-modal>
      </div>
</div>